#!/usr/bin/env python3
"""
Simple HTTP server to serve the Riva Gleam frontend
This avoids CORS issues when loading local files
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

# Set the port
PORT = 3000

# Change to the project directory
project_dir = Path(__file__).parent
os.chdir(project_dir)

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

def main():
    try:
        with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
            print(f"🌐 Serving Riva Gleam frontend at http://localhost:{PORT}")
            print(f"📁 Serving files from: {project_dir}")
            print(f"🚀 Open your browser to: http://localhost:{PORT}")
            print("Press Ctrl+C to stop the server")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Port {PORT} is already in use. Try a different port or stop the existing server.")
        else:
            print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
