#!/bin/bash

# Riva Gleam Development Startup Script
# This script builds the frontend and provides instructions for running the full stack

echo "🚀 Starting Riva Gleam Development Environment"
echo "=============================================="

# Build the frontend
echo "📦 Building frontend (JavaScript target)..."
gleam build --target javascript

if [ $? -eq 0 ]; then
    echo "✅ Frontend built successfully!"
    echo ""
    echo "🌐 Frontend is ready at: file://$(pwd)/index.html"
    echo ""
    echo "📋 Next steps:"
    echo "1. Open the frontend in your browser: file://$(pwd)/index.html"
    echo "2. The frontend currently uses mock API responses"
    echo "3. To implement real API integration:"
    echo "   - Complete the API server implementation in src/api_server.gleam"
    echo "   - Add proper HTTP client for the target platform"
    echo "   - Start the API server on port 8080"
    echo ""
    echo "🎯 Current features:"
    echo "- ✅ Counter with Increment/Decrement/Reset buttons"
    echo "- ✅ Loading states for API calls"
    echo "- ✅ Mock API integration with effects"
    echo "- ⏳ Real HTTP API integration (next step)"
    echo ""
    echo "Happy coding! 🎉"
else
    echo "❌ Frontend build failed!"
    exit 1
fi
