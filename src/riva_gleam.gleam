import gleam/list
import gleam/int
import lustre
import lustre/element.{type Element}
import lustre/element/html
import lustre/effect.{type Effect}

import button

// TYPES -----------------------------------------------------------------------

pub type Model {
  Model(count: Int, loading: Bool)
}

pub type Msg {
  // Local actions
  Increment
  Decrement
  Reset
  // API actions
  FetchCount
  UpdateCount(String)
  // API responses
  CountFetched(Result(Int, String))
  CountUpdated(Result(Int, String))
}

// API FUNCTIONS ---------------------------------------------------------------
// For now, simulate API calls with effects that return mock data
// In a real app, you'd use an HTTP client library for the target platform

fn fetch_count() -> Effect(Msg) {
  effect.from(fn(dispatch) {
    // Simulate API call - in real app, make HTTP request here
    dispatch(CountFetched(Ok(42)))
  })
}

fn update_count_api(action: String) -> Effect(Msg) {
  effect.from(fn(dispatch) {
    // Simulate API call - in real app, make HTTP request here
    let new_count = case action {
      "increment" -> 43
      "decrement" -> 41
      "reset" -> 0
      _ -> 42
    }
    dispatch(CountUpdated(Ok(new_count)))
  })
}

// INIT ------------------------------------------------------------------------

fn init(_flags) -> #(Model, Effect(Msg)) {
  #(Model(count: 0, loading: False), fetch_count())
}

// UPDATE ----------------------------------------------------------------------

fn update(model: Model, msg: Msg) -> #(Model, Effect(Msg)) {
  case msg {
    // API actions - make HTTP requests
    Increment -> #(Model(..model, loading: True), update_count_api("increment"))
    Decrement -> #(Model(..model, loading: True), update_count_api("decrement"))
    Reset -> #(Model(..model, loading: True), update_count_api("reset"))

    // Manual API actions
    FetchCount -> #(Model(..model, loading: True), fetch_count())
    UpdateCount(action) -> #(Model(..model, loading: True), update_count_api(action))

    // API responses
    CountFetched(Ok(count)) -> #(Model(count: count, loading: False), effect.none())
    CountFetched(Error(_)) -> #(Model(..model, loading: False), effect.none())
    CountUpdated(Ok(count)) -> #(Model(count: count, loading: False), effect.none())
    CountUpdated(Error(_)) -> #(Model(..model, loading: False), effect.none())
  }
}

// VIEW ------------------------------------------------------------------------


fn render_elements(model: Model) -> List(Element(Msg)) {
  list.map(
    list.range(0, model.count),
    fn(el) { html.li([], [element.text(int.to_string(el))]) }
  )
}



fn buttons(model: Model) -> Element(Msg) {
let count = model.count |> int.to_string

  html.div([], [
    html.h1([], [element.text("Riva Gleam Counter")]),
    html.p([], [element.text("Count: " <> count)]),
    case model.loading {
      True -> html.p([], [element.text("Loading...")])
      False -> html.div([], [])
    },
    button.primary("+", Increment),
    button.secondary("-", Decrement),
    button.danger("Reset", Reset)
  ])
}




fn view(model: Model) -> Element(Msg) {
  html.div([], [
    buttons(model),
    html.ul([], render_elements(model))
  ])
}

// MAIN ------------------------------------------------------------------------

pub fn main() -> Nil {
  let app = lustre.application(init, update, view)
  let assert Ok(_) = lustre.start(app, "#app", Nil)
  Nil
}
