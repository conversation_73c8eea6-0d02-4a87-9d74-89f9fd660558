import gleam/list
import gleam/int
import lustre
import lustre/element.{type Element}
import lustre/element/html

import button

// TYPES -----------------------------------------------------------------------

pub type Model =
  Int

pub type Msg {
  Increment
  Decrement
  Reset
}

// INIT ------------------------------------------------------------------------

fn init(_flags) -> Model {
  0
}

// UPDATE ----------------------------------------------------------------------

fn update(model: Model, msg: Msg) -> Model {
  case msg {
    Increment -> model + 1
    Decrement -> model - 1
    Reset -> 0
  }
}

// VIEW ------------------------------------------------------------------------


fn render_elements(model: Model) -> List(Element(Msg)) {
  list.map(
    list.range(0, model),
    fn(el) { html.li([], [element.text(int.to_string(el))]) }
  )
}



fn buttons(model: Model) -> Element(Msg) {
let count = model |> int.to_string

  html.div([], [
    html.h1([], [element.text("Riva Gleam Counter")]),
    html.p([], [element.text("Count: " <> count)]),
    button.primary("+", Increment),
    button.secondary("-", Decrement),
    button.danger("Reset", Reset)
  ])
}




fn view(model: Model) -> Element(Msg) {
  html.div([], [
    buttons(model),
    html.ul([], render_elements(model))
  ])
}

// MAIN ------------------------------------------------------------------------

pub fn main() -> Nil {
  let app = lustre.simple(init, update, view)
  let assert Ok(_) = lustre.start(app, "#app", Nil)
  Nil
}
