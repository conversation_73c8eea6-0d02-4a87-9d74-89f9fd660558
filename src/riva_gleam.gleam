import gleam/list
import gleam/int
import lustre
import lustre/element.{type Element}
import lustre/element/html
import lustre/effect.{type Effect}
import gleam/fetch
import gleam/http
import gleam/http/request
import gleam/javascript/promise
import gleam/json
import gleam/dynamic/decode

import button

// TYPES -----------------------------------------------------------------------

pub type Model {
  Model(count: Int, loading: Bool)
}

pub type Msg {
  // Local actions
  Increment
  Decrement
  Reset
  // API actions
  FetchCount
  UpdateCount(String)
  // API responses
  CountFetched(Result(Int, String))
  CountUpdated(Result(Int, String))
}

// API FUNCTIONS ---------------------------------------------------------------
// Real API calls using gleam_fetch

fn fetch_count() -> Effect(Msg) {
  effect.from(fn(dispatch) {
    let req = request.new()
      |> request.set_method(http.Get)
      |> request.set_host("localhost")
      |> request.set_port(3001)
      |> request.set_path("/api/count")
      |> request.set_header("content-type", "application/json")

    fetch.send(req)
    |> promise.try_await(fetch.read_json_body)
    |> promise.map(fn(result) {
      case result {
        Ok(response) -> {
          let count_decoder = {
            use count <- decode.field("count", decode.int)
            decode.success(count)
          }
          case decode.run(response.body, count_decoder) {
            Ok(count) -> dispatch(CountFetched(Ok(count)))
            Error(_) -> dispatch(CountFetched(Error("Failed to decode response")))
          }
        }
        Error(_) -> dispatch(CountFetched(Error("Network error")))
      }
    })
    |> promise.rescue(fn(_) {
      dispatch(CountFetched(Error("Request failed")))
      Nil
    })

    Nil
  })
}

fn update_count_api(action: String) -> Effect(Msg) {
  effect.from(fn(dispatch) {
    let body = json.object([#("action", json.string(action))])
      |> json.to_string

    let req = request.new()
      |> request.set_method(http.Post)
      |> request.set_host("localhost")
      |> request.set_port(3001)
      |> request.set_path("/api/count")
      |> request.set_header("content-type", "application/json")
      |> request.set_body(body)

    fetch.send(req)
    |> promise.try_await(fetch.read_json_body)
    |> promise.map(fn(result) {
      case result {
        Ok(resp) -> {
          let count_decoder = {
            use count <- decode.field("count", decode.int)
            decode.success(count)
          }
          case decode.run(resp.body, count_decoder) {
            Ok(count) -> dispatch(CountUpdated(Ok(count)))
            Error(_) -> dispatch(CountUpdated(Error("Failed to decode response")))
          }
        }
        Error(_) -> dispatch(CountUpdated(Error("Network error")))
      }
    })
    |> promise.rescue(fn(_) {
      dispatch(CountUpdated(Error("Request failed")))
      Nil
    })

    Nil
  })
}

// INIT ------------------------------------------------------------------------

fn init(_flags) -> #(Model, Effect(Msg)) {
  #(Model(count: 0, loading: False), fetch_count())
}

// UPDATE ----------------------------------------------------------------------

fn update(model: Model, msg: Msg) -> #(Model, Effect(Msg)) {
  case msg {
    // API actions - make HTTP requests
    Increment -> #(Model(..model, loading: True), update_count_api("increment"))
    Decrement -> #(Model(..model, loading: True), update_count_api("decrement"))
    Reset -> #(Model(..model, loading: True), update_count_api("reset"))

    // Manual API actions
    FetchCount -> #(Model(..model, loading: True), fetch_count())
    UpdateCount(action) -> #(Model(..model, loading: True), update_count_api(action))

    // API responses
    CountFetched(Ok(count)) -> #(Model(count: count, loading: False), effect.none())
    CountFetched(Error(_)) -> #(Model(..model, loading: False), effect.none())
    CountUpdated(Ok(count)) -> #(Model(count: count, loading: False), effect.none())
    CountUpdated(Error(_)) -> #(Model(..model, loading: False), effect.none())
  }
}

// VIEW ------------------------------------------------------------------------


fn render_elements(model: Model) -> List(Element(Msg)) {
  list.map(
    list.range(0, model.count),
    fn(el) { html.li([], [element.text(int.to_string(el))]) }
  )
}



fn buttons(model: Model) -> Element(Msg) {
let count = model.count |> int.to_string

  html.div([], [
    html.h1([], [element.text("Riva Gleam Counter")]),
    html.p([], [element.text("Count: " <> count)]),
    case model.loading {
      True -> html.p([], [element.text("Loading...")])
      False -> html.div([], [])
    },
    button.primary("+", Increment),
    button.secondary("-", Decrement),
    button.danger("Reset", Reset)
  ])
}




fn view(model: Model) -> Element(Msg) {
  html.div([], [
    buttons(model),
    html.ul([], render_elements(model))
  ])
}

// MAIN ------------------------------------------------------------------------

pub fn main() -> Nil {
  let app = lustre.application(init, update, view)
  let assert Ok(_) = lustre.start(app, "#app", Nil)
  Nil
}
