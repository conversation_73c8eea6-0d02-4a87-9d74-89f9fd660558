import gleam/int
import gleam/json
import wisp.{type Request, type Response}
import gleam/http.{Get, Post}

// SIMPLE STATELESS API (for demo purposes)
// In a real app, you'd use a database or proper state management

// For this demo, we'll use a simple counter that resets on each request
// In a real application, you'd persist this in a database

// HANDLERS --------------------------------------------------------------------

pub fn handle_request(req: Request) -> Response {
  // Add CORS headers to all responses
  let response = case req.method, wisp.path_segments(req) {
    Get, ["api", "count"] -> get_count()
    Post, ["api", "count"] -> update_count(req)
    _, _ -> wisp.not_found()
  }

  // Add CORS headers to response
  response
  |> wisp.set_header("Access-Control-Allow-Origin", "*")
  |> wisp.set_header("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
  |> wisp.set_header("Access-Control-Allow-Headers", "Content-Type")
}

fn get_count() -> Response {
  // For demo purposes, return a static count
  // In a real app, you'd fetch this from a database
  let json_response = json.object([
    #("count", json.int(42))
  ])

  json_response
  |> json.to_string_tree
  |> wisp.json_response(200)
}

fn update_count(req: Request) -> Response {
  use _json_data <- wisp.require_json(req)

  // For now, just increment the count
  // In a real app, you'd parse the JSON and update based on the action
  let new_count = 43

  let json_response = json.object([
    #("count", json.int(new_count))
  ])

  json_response
  |> json.to_string_tree
  |> wisp.json_response(200)
}

// SERVER SETUP ----------------------------------------------------------------

pub fn start_server() -> Nil {
  // For now, just a placeholder
  // In a real app, you'd use mist to start the server
  // let assert Ok(_) = mist.new(handle_request)
  //   |> mist.port(8080)
  //   |> mist.start_http
  Nil
}
